using WMS.APP.Common;
using WMS.APP.ViewModel;

namespace WMS.APP;

public partial class LoginPage : ContentPage
{

    //private readonly IServiceProvider _serviceProvider;

    //private readonly INavigationService _navigation;

    public LoginPage(LoginViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;
        // ���ؼ�ס������
        var vm = (LoginViewModel)BindingContext;
        vm.LoadSavedCredentials();
    }

    private void OnRememberPasswordTapped(object sender, EventArgs e)
    {
        var vm = (LoginViewModel)BindingContext;
        vm.RememberPassword = !vm.RememberPassword;
    }

}
