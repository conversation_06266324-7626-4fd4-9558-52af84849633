using CommunityToolkit.Mvvm.Messaging;
using System.Diagnostics;
using System.Runtime.Versioning;
using WMS.APP.Message;
using WMS.APP.ViewModels.outbound;

namespace WMS.APP.Views.outbound;

public partial class PkmScanPage : ContentPage
{
    private PkmScanViewModel ViewModel => BindingContext as PkmScanViewModel;
    private bool _isManualInput = false; // 标记是否为手动输入模式
    private Entry _lastFocusedEntry = null; // 记录最后获得焦点的Entry
    private bool _isInitialized = false; // 标记是否已初始化

    public PkmScanPage(PkmScanViewModel viewModel)
    {
        InitializeComponent();
        BindingContext = viewModel;

        // 注册扫描器结果消息
        WeakReferenceMessenger.Default.Register<ScannerResultMessage>(this, (r, m) =>
        {
            _isManualInput = false; // 扫描模式
            ViewModel?.HandleScanInput(m.Result);

            // 扫描后确保所有Entry失去焦点并隐藏键盘
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await HideAllKeyboardsAndCursors();
            });
        });

        // 延迟初始化，确保UI完全加载
        Loaded += OnPageLoaded;
    }

    private void OnPageLoaded(object sender, EventArgs e)
    {
        if (!_isInitialized)
        {
            _isInitialized = true;
            // 初始化：默认OrderEntry高亮，但不获得焦点
            ViewModel?.InitializeEntries();

            // 确保所有Entry都没有焦点，键盘和光标隐藏
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await HideAllKeyboardsAndCursors();
            });
        }
    }

    /*private void OnEntryCompleted(object sender, EventArgs e)
    {
        if (sender is Entry entry)
        {
            // 确保在完成输入后隐藏光标
            MainThread.BeginInvokeOnMainThread(() =>
            {
                //entry.Unfocus(); // 取消焦点，关闭光标 & 键盘
            });

            var fieldId = entry.StyleId ?? GetEntryFieldId(entry);
            Debug.WriteLine($"Current FieldId: {fieldId}");
            var isLastField = entry == QuantityEntry;

            if (isLastField)
            {
                ViewModel?.HandleManualCompleted();
            }
            else
            {
                ViewModel?.HandleScanInputByFieldId(fieldId, entry.Text);
            }
        }
    }*/

    private async void OnEntryCompleted(object sender, EventArgs e)
    {
        if (sender is Entry entry)
        {
            await Task.Delay(50);
            var fieldId = GetEntryFieldId(entry);
            var inputText = entry.Text?.Trim() ?? string.Empty;

            // 立即隐藏键盘和光标
            //await HideKeyboardAndCursor(entry);

            // 处理输入逻辑
            if (_isManualInput)
            {
                // 手动输入模式
                if (string.IsNullOrWhiteSpace(inputText))
                {
                    // 输入为空，保持当前高亮，键盘隐藏
                    return;
                }
                else
                {
                    // 输入不为空，跳转到下一个空白字段
                    ViewModel?.HandleScanInputByFieldId(fieldId, inputText);

                    // 确保跳转后的字段也隐藏键盘和光标
                    //await Task.Delay(100);
                    //await HideAllKeyboardsAndCursors();
                }
            }
            else
            {
                // 扫描模式或其他模式
                var isLastField = entry == QuantityEntry;
                if (isLastField)
                {
                    ViewModel?.HandleManualCompleted();
                }
                else
                {
                    ViewModel?.HandleScanInputByFieldId(fieldId, inputText);
                }
            }
        }
    }


    private void OnEntryFocused(object sender, FocusEventArgs e)
    {
        if (sender is Entry entry)
        {
            Debug.WriteLine("进入第一次人工点击：" + entry);
            _isManualInput = true; // 标记为手动输入模式
            _lastFocusedEntry = entry;

            var fieldId = GetEntryFieldId(entry);

            // 手动点击时，高亮跳转到点击的位置
            ViewModel?.SetActiveEntryByFieldId(fieldId);
        }
    }

    private void OnEntryUnfocused(object sender, FocusEventArgs e)
    {
        // Entry失去焦点时的处理
        if (sender is Entry entry)
        {
            Debug.WriteLine("进入第一次人工失去焦点：" + entry);
            // 延迟一点时间确保焦点完全转移
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                await Task.Delay(50);
                //await HideKeyboardAndCursor(entry);
            });
        }
    }

    private string GetEntryFieldId(Entry entry)
    {
        if (entry == OrderEntry) return "OrderEntry";
        if (entry == MaterialEntry) return "MaterialEntry";
        if (entry == BatchEntry) return "BatchEntry";
        if (entry == QuantityEntry) return "QuantityEntry";
        return "Unknown";
    }

    /// <summary>
    /// 隐藏所有Entry的键盘和光标
    /// </summary>
    private async Task HideAllKeyboardsAndCursors()
    {
        var entries = new[] { OrderEntry, MaterialEntry, BatchEntry, QuantityEntry };

        foreach (var entry in entries)
        {
            if (entry != null)
            {
                await HideKeyboardAndCursor(entry);
            }
        }
    }

    /// <summary>
    /// 隐藏指定Entry的键盘和光标
    /// </summary>
    private async Task HideKeyboardAndCursor(Entry entry)
    {
        if (entry == null) return;

        try
        {
            // 确保在主线程执行
            if (MainThread.IsMainThread)
            {
                entry.Unfocus();

                // Android 7.1及以下版本的兼容处理
                if (DeviceInfo.Platform == DevicePlatform.Android)
                {
                    // 强制隐藏软键盘
#if ANDROID
                    var platformView = entry.Handler?.PlatformView;
                    if (platformView is Android.Widget.EditText editText)
                    {
                        editText.ClearFocus();
                        editText.SetCursorVisible(false);

                        var inputMethodManager = editText.Context?.GetSystemService(Android.Content.Context.InputMethodService) as Android.Views.InputMethods.InputMethodManager;
                        inputMethodManager?.HideSoftInputFromWindow(editText.WindowToken, Android.Views.InputMethods.HideSoftInputFlags.None);
                    }
#endif
                }
            }
            else
            {
                await MainThread.InvokeOnMainThreadAsync(() =>
                {
                    entry.Unfocus();
                });
            }

            // 短暂延迟确保操作完成
            await Task.Delay(50);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"HideKeyboardAndCursor error: {ex.Message}");
        }
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        OrderEntry.IsEnabled = false;
        Task.Delay(300);
        OrderEntry.IsEnabled = true;
        // 取消注册消息接收器
        WeakReferenceMessenger.Default.Unregister<ScannerResultMessage>(this);
    }

    private async void FocusEntryByFieldId(string fieldId)
    {
        await Task.Delay(100); // 给 Android 7.x 一些缓冲时间

        switch (fieldId)
        {
            case "OrderEntry":
                OrderEntry.Focus(); break;
            case "MaterialEntry":
                MaterialEntry.Focus(); break;
            case "BatchEntry":
                BatchEntry.Focus(); break;
            case "QuantityEntry":
                QuantityEntry.Focus(); break;
        }
    }

    private string? GetNextEmptyFieldId(string currentFieldId)
    {
        var fieldOrder = new[] { "OrderEntry", "MaterialEntry", "BatchEntry", "QuantityEntry" };
        var data = new[] { ViewModel?.OrderNumber, ViewModel?.MaterialCode, ViewModel?.BatchNumber, ViewModel?.OrderQuantity };

        int currentIndex = Array.IndexOf(fieldOrder, currentFieldId);
        if (currentIndex == -1) return null;

        for (int i = currentIndex + 1; i < fieldOrder.Length; i++)
        {
            if (string.IsNullOrEmpty(data[i]))
                return fieldOrder[i];
        }

        return null;
    }
}
