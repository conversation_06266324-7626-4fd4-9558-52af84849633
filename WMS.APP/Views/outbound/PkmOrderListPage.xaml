<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
             x:Class="WMS.APP.Views.outbound.PkmOrderListPage"
             Title="出库单查询">

    <ContentPage.Resources>
        <Style x:Key="SearchEntryStyle" TargetType="Entry">
            <Setter Property="HeightRequest" Value="35"/>
            <Setter Property="Margin" Value="0,5"/>
        </Style>
    </ContentPage.Resources>

    <Grid RowDefinitions="Auto,*" Padding="10">
        <!-- 搜索区域 -->
        <VerticalStackLayout Grid.Row="0" Spacing="5">
            <Entry Placeholder="系统订单号" 
                   Text="{Binding SystemOrderNo}"
                   Style="{StaticResource SearchEntryStyle}"/>
            <Entry Placeholder="客户订单号" 
                   Text="{Binding CustomerOrderNo}"
                   Style="{StaticResource SearchEntryStyle}"/>

            <!-- 更多查询条件按钮 -->
            <!--<Button Text="更多查询条件" 
                    Command="{Binding ShowMoreFiltersCommand}"
                    Margin="0,5"/>-->

            <!-- 显示已选日期范围 -->
            <Label IsVisible="{Binding HasDateFilter}">
                <Label.FormattedText>
                    <FormattedString>
                        <Span Text="日期范围: "/>
                        <Span Text="{Binding StartDate, StringFormat='{0:yyyy-MM-dd}'}"/>
                        <Span Text=" 至 "/>
                        <Span Text="{Binding EndDate, StringFormat='{0:yyyy-MM-dd}'}"/>
                    </FormattedString>
                </Label.FormattedText>
            </Label>
            <Button Text="搜索"
                    Command="{Binding SearchCommand}"
                    BackgroundColor="{StaticResource Primary}"
                    TextColor="White"
                    HeightRequest="45"
                    Margin="0,5"/>
        </VerticalStackLayout>

        <!-- 订单列表 -->
        <RefreshView Grid.Row="1" 
                     Command="{Binding RefreshCommand}"
                     IsRefreshing="{Binding IsRefreshing}">
            <CollectionView ItemsSource="{Binding Orders}"
                          RemainingItemsThreshold="1"
                          RemainingItemsThresholdReachedCommand="{Binding LoadMoreCommand}">
                <CollectionView.ItemTemplate>
                    <DataTemplate>
                        <Frame Margin="0,5" Padding="10" BorderColor="{StaticResource Primary}">
                            <Grid RowDefinitions="Auto,Auto,Auto,Auto" RowSpacing="5">
                                <Grid Grid.Row="0" ColumnDefinitions="Auto,*,Auto">
                                    <Label Grid.Column="0" Text="系统单号：" FontAttributes="Bold"/>
                                    <Label Grid.Column="1" Text="{Binding orderNo}"/>
                                    <Label Grid.Column="2" Text="{Binding orderType}" 
                                           TextColor="{StaticResource Primary}"
                                           FontAttributes="Bold"/>
                                </Grid>

                                <StackLayout Grid.Row="1" Orientation="Horizontal">
                                    <Label Text="客户单号：" FontAttributes="Bold"/>
                                    <Label Text="{Binding orderCon}"/>
                                </StackLayout>

                                <Grid Grid.Row="2" ColumnDefinitions="Auto,*,Auto,Auto">
                                    <Label Grid.Column="0" Text="货主：" FontAttributes="Bold"/>
                                    <Label Grid.Column="1" Text="{Binding owner}"/>
                                    <Label Grid.Column="2" Text="日期：" FontAttributes="Bold"/>
                                    <Label Grid.Column="3" Text="{Binding orderDate, StringFormat='{0:yyyy-MM-dd}'}"/>
                                </Grid>

                                <StackLayout Grid.Row="3" Orientation="Horizontal">
                                    <Label Text="备注：" FontAttributes="Bold"/>
                                    <Label Text="{Binding orderRemark}" LineBreakMode="TailTruncation"/>
                                </StackLayout>
                            </Grid>
                        </Frame>
                    </DataTemplate>
                </CollectionView.ItemTemplate>
                <CollectionView.EmptyView>
                    <VerticalStackLayout HorizontalOptions="Center" 
                                       VerticalOptions="Center">
                        <Label Text="暂无订单数据" 
                               HorizontalOptions="Center"/>
                    </VerticalStackLayout>
                </CollectionView.EmptyView>
            </CollectionView>
        </RefreshView>
    </Grid>

    <!-- 加载指示器 -->
    <!--<toolkit:Loading x:Name="LoadingIndicator" 
                     IsVisible="{Binding IsBusy}"/>-->

</ContentPage>