<?xml version="1.0" encoding="utf-8" ?>
<!--<toolkit:Popup xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
               xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
               xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
               x:Class="WMS.APP.Views.outbound.DateFilterPopup">
    <VerticalStackLayout Padding="20" Spacing="10" BackgroundColor="White">
        
    </VerticalStackLayout>
</toolkit:Popup>-->
<toolkit:Popup
    x:Class="WMS.APP.Views.outbound.DateFilterPopup"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit">
	<!-- 设置弹窗背景遮罩（半透明黑） -->
	<Frame
        BackgroundColor="White"
        CornerRadius="12"
        Padding="20"
        WidthRequest="320"
        HasShadow="True">

		<VerticalStackLayout Spacing="12">
			<!-- 查询项控件 -->
			<Label Text="选择日期范围"
               FontSize="18"
               FontAttributes="Bold"
               HorizontalOptions="Center"/>

			<VerticalStackLayout Spacing="5">
				<Label Text="开始日期"/>
				<DatePicker Date="{Binding StartDate}"
						   Format="yyyy-MM-dd"/>
			</VerticalStackLayout>

			<VerticalStackLayout Spacing="5">
				<Label Text="截至日期"/>
				<DatePicker Date="{Binding EndDate}"
						   Format="yyyy-MM-dd"/>
			</VerticalStackLayout>

			<HorizontalStackLayout Spacing="10"
								 HorizontalOptions="Center">
				<Button Text="确认"
						Command="{Binding ConfirmCommand}"
						BackgroundColor="{StaticResource Primary}"
						TextColor="White"/>
				<Button Text="取消"
						Command="{Binding CancelCommand}"
						BackgroundColor="Gray"
						TextColor="White"/>
			</HorizontalStackLayout>
		</VerticalStackLayout>

	</Frame>
</toolkit:Popup>