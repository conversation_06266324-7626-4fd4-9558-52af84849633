# PkmScanPage 自定义控件转换说明

## 转换概述
将 `PkmScanPage` 中的自定义控件 `ScanEntryView` 替换为标准的 `Label/Entry` 组件，保持原有功能不变。

## 主要变更

### 1. XAML 文件变更 (PkmScanPage.xaml)

**原始代码：**
```xml
<controls:ScanEntryView x:Name="Order_Scan" Title="订单号" ControlId="Order_Scan" ScanText="{Binding OrderNumber}" />
```

**转换后：**
```xml
<VerticalStackLayout Padding="5">
    <Label Text="订单号" FontSize="15" FontAttributes="Bold"/>
    <Entry x:Name="OrderEntry"
           Text="{Binding OrderNumber, Mode=TwoWay}"
           Completed="OnEntryCompleted"
           Focused="OnEntryFocused"
           Unfocused="OnEntryUnfocused"
           ReturnType="Next"
           FontSize="15"
           HeightRequest="45"
           Placeholder="请输入或扫码"
           BackgroundColor="{Binding OrderEntryBackgroundColor}"/>
</VerticalStackLayout>
```

### 2. ViewModel 变更 (PkmScanViewModel.cs)

**移除的依赖：**
- 移除了 `using WMS.APP.UserControl;`
- 移除了 `ScanEntryView[] scanControls;`
- 移除了 `SetScanControls` 方法

**新增的属性：**
- `OrderEntryBackgroundColor` - 订单号输入框背景色
- `MaterialEntryBackgroundColor` - 物料编号输入框背景色  
- `BatchEntryBackgroundColor` - 批次号输入框背景色
- `QuantityEntryBackgroundColor` - 数量输入框背景色

**修改的方法：**
- `HandleScanInputByFiledId` → `HandleScanInputByFieldId` (修正拼写错误)
- 控件ID从 "Order_Scan" 改为 "OrderEntry"
- `HighlightNextEmpty` 方法改为设置背景颜色而不是控件的 IsActive 属性

### 3. Code-behind 变更 (PkmScanPage.xaml.cs)

**新增的事件处理：**
- `OnEntryCompleted` - 处理输入完成事件
- `OnEntryFocused` - 处理获得焦点事件  
- `OnEntryUnfocused` - 处理失去焦点事件
- `GetEntryFieldId` - 获取输入框的字段ID

**移除的功能：**
- 移除了 `ScanCompletedMessage` 的注册和处理
- 移除了 `SetScanControls` 调用

## 功能保持

### 扫描功能
- ✅ 支持扫描器输入 (通过 `ScannerResultMessage`)
- ✅ 自动分配到对应字段
- ✅ 扫描后自动跳转到下一个空字段

### 手动输入功能  
- ✅ 支持手动输入
- ✅ 输入完成后自动跳转
- ✅ 最后一个字段完成后自动提交

### 视觉反馈
- ✅ 当前活跃字段高亮显示 (黄色背景)
- ✅ 智能焦点管理
- ✅ 光标自动隐藏

### 数据验证
- ✅ 所有字段必填验证
- ✅ 提交按钮状态控制
- ✅ 数量字段数字键盘

## 测试要点

1. **扫描测试：**
   - 使用扫描器扫描条码，验证自动填充和跳转
   - 验证扫描后光标隐藏

2. **手动输入测试：**
   - 手动输入各字段，验证完成后跳转
   - 验证最后字段完成后自动提交

3. **焦点管理测试：**
   - 点击不同输入框，验证焦点正确设置
   - 验证背景色高亮效果

4. **提交功能测试：**
   - 验证所有字段填写后可以提交
   - 验证提交后字段清空和重置

## 优势

1. **减少依赖：** 不再依赖自定义控件
2. **更好的维护性：** 使用标准MAUI控件
3. **更好的性能：** 减少了控件层次
4. **更灵活的样式：** 可以直接修改标准控件样式
