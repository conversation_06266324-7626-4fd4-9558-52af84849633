using CommunityToolkit.Maui.Views;
using CommunityToolkit.Mvvm.Input;

namespace WMS.APP.Views.outbound
{
    public partial class DateFilterPopup : Popup
    {
        public event EventHandler<DateFilterEventArgs> DateSelected;

        private DateTime? _startDate;
        private DateTime? _endDate;

        public DateFilterPopup(DateTime? startDate = null, DateTime? endDate = null)
        {
            InitializeComponent();
            _startDate = startDate;
            _endDate = endDate;
            BindingContext = this;
        }

        public DateTime? StartDate
        {
            get => _startDate;
            set
            {
                _startDate = value;
                OnPropertyChanged();
            }
        }

        public DateTime? EndDate
        {
            get => _endDate;
            set
            {
                _endDate = value;
                OnPropertyChanged();
            }
        }

        [RelayCommand]
        private async Task Confirm()
        {
            DateSelected?.Invoke(this, new DateFilterEventArgs(StartDate, EndDate));
            await CloseAsync();
        }

        [RelayCommand]
        private async Task Cancel()
        {
            await CloseAsync();
        }
    }

    public class DateFilterEventArgs : EventArgs
    {
        public DateTime? StartDate { get; }
        public DateTime? EndDate { get; }

        public DateFilterEventArgs(DateTime? startDate, DateTime? endDate)
        {
            StartDate = startDate;
            EndDate = endDate;
        }
    }
}