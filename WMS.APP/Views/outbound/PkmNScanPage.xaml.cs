using WMS.APP.Utils;

namespace WMS.APP.Views.outbound;

public partial class PkmNScanPage : ContentPage
{
    private string _activeField = "OrderNumber";
    private readonly ScanFormManager _formManager = new();
    private readonly List<string> _fieldOrder = new() { "OrderNumber", "MaterialCode", "BatchNumber", "Quantity" };

    public PkmNScanPage()
    {
        InitializeComponent();

        _formManager.RegisterField("Order", OrderEntry);
        _formManager.RegisterField("Material", MaterialEntry);
        _formManager.RegisterField("Bin", BinEntry);
        _formManager.RegisterField("Qty", QtyEntry);

        _formManager.SetInitialField("Order");

        _formManager.OnFormCompleted += () =>
        {
            DisplayAlert("���", "ɨ��¼����ɣ���׼���ύ��", "OK");
        };
    }
    private void OnEntryCompleted(object sender, EventArgs e)
    {
        var text = ((Entry)sender).Text;
        _formManager.HandleCompleted(text);
    }

    private void OnEntryTapped(object sender, EventArgs e)
    {
        if (sender is Entry entry && entry.StyleId != null)
        {
            _formManager.ActivateField(entry.StyleId);
        }
    }
}