# PkmScanPage API校验功能优化说明

## 功能概述
为PkmScanPage添加了完整的API校验功能，实现了扫描和手动输入时的数据验证、错误提示和数据提交功能。

## 主要功能

### 1. 订单号校验
- **触发时机**：扫描或输入订单号时
- **校验逻辑**：
  - 检查订单号是否为空
  - 调用API验证订单号是否存在
- **成功**：跳转到下一个字段（SKU）
- **失败**：显示"单号不存在"提示，2秒后清空当前输入，保持高亮

### 2. SKU校验
- **触发时机**：扫描或输入SKU时
- **校验逻辑**：
  - 检查订单号是否为空
  - 检查SKU是否为空
  - 验证订单号是否存在
  - 验证SKU是否存在于指定订单中
- **成功**：跳转到下一个字段（批次号）
- **失败**：显示相应错误提示，2秒后清空当前输入

### 3. 批次号校验
- **触发时机**：扫描或输入批次号时
- **校验逻辑**：
  - 检查订单号、SKU是否已填写
  - 检查批次号是否为空
  - 验证批次号是否存在于指定订单和SKU中
- **成功**：
  - 跳转到数量字段
  - 显示需要拣货的数量提示
- **失败**：显示"批次号不存在"提示，2秒后清空当前输入

### 4. 数量显示和提交
- **数量提示**：批次号验证成功后，显示"需要拣货数量: XX"
- **数量输入**：支持手动输入数量
- **提交校验**：
  - 验证数量是否有效（大于0的数字）
  - 调用API提交拣货数据
- **成功**：显示"订单提交成功"，清空所有字段
- **失败**：显示错误提示，清空数量字段

## 技术实现

### 1. 服务接口扩展 (IOutboundService)
```csharp
// 校验相关方法
Task<bool> ValidateOrderNumberAsync(string orderNumber);
Task<bool> ValidateSkuCodeAsync(string orderNumber, string skuCode);
Task<bool> ValidateBatchNumberAsync(string orderNumber, string skuCode, string batchNumber);
Task<decimal> GetRequiredQuantityAsync(string orderNumber, string skuCode, string batchNumber);
Task<bool> SubmitPickingDataAsync(string orderNumber, string skuCode, string batchNumber, decimal quantity);
```

### 2. 数据模型 (PkmOrderDetailModel)
```csharp
public class PkmOrderDetailModel
{
    public string OrderNo { get; set; }
    public string SkuCode { get; set; }
    public string SkuName { get; set; }
    public string BatchNumber { get; set; }
    public decimal RequiredQuantity { get; set; }
    public decimal PickedQuantity { get; set; }
    public string Status { get; set; }
}
```

### 3. ViewModel优化 (PkmScanViewModel)
- **依赖注入**：注入IOutboundService
- **异步校验**：所有校验方法改为异步
- **错误处理**：统一的错误提示和清空逻辑
- **数量提示**：添加QuantityHint属性显示需要拣货数量

### 4. UI优化 (PkmScanPage.xaml)
- **数量提示标签**：显示需要拣货的数量
- **依赖注入**：通过构造函数注入ViewModel

## 错误处理机制

### 1. 提示方式
- 使用`DisplayAlert`显示错误信息
- 提示框无需用户确认，自动关闭

### 2. 清空逻辑
- 错误提示显示2秒后自动清空当前字段
- 不自动跳转到下一个控件
- 保持当前字段高亮状态

### 3. 异常处理
- 网络异常、API异常统一捕获
- 显示具体错误信息给用户
- 确保应用不会崩溃

## 工作流程

### 扫描模式流程
1. **扫描订单号** → API校验 → 成功：跳转SKU / 失败：提示+清空
2. **扫描SKU** → API校验 → 成功：跳转批次 / 失败：提示+清空
3. **扫描批次** → API校验 → 成功：跳转数量+显示提示 / 失败：提示+清空
4. **输入数量** → 提交API → 成功：清空所有字段 / 失败：提示+清空数量

### 手动输入模式流程
- 与扫描模式相同的校验逻辑
- 支持点击任意字段开始输入
- 每个字段完成后进行相应校验

## 模拟数据

### 测试订单号
- SO20241225XXXX (当天生成的订单号)
- 共50个测试订单

### 测试SKU
- SKU001 (iPhone 15)
- SKU002 (华为 Mate 60)
- SKU003 (小米 14)
- SKU004 (OPPO Find X7)
- SKU005 (VIVO X100)

### 测试批次号
- BATCH001
- BATCH002
- BATCH003
- BATCH004
- BATCH005

## 配置要求

### 1. 依赖注入配置
确保在MauiProgram.cs中正确注册：
```csharp
builder.Services.AddScoped<IOutboundService, OutboundService>();
builder.Services.AddTransient<PkmScanViewModel>();
builder.Services.AddTransient<PkmScanPage>();
```

### 2. 导航配置
页面通过依赖注入创建，确保导航时使用正确的方式。

## 测试要点

### 1. 正常流程测试
- 使用有效的订单号、SKU、批次号进行完整流程测试
- 验证数量提示是否正确显示
- 验证提交成功后字段清空

### 2. 异常流程测试
- 输入无效订单号，验证错误提示
- 输入不匹配的SKU，验证错误提示
- 输入无效批次号，验证错误提示
- 输入无效数量，验证错误提示

### 3. 用户体验测试
- 验证错误提示2秒后自动消失
- 验证字段清空后高亮保持
- 验证不会自动跳转到下一个控件

### 4. 网络异常测试
- 模拟网络异常，验证错误处理
- 验证应用稳定性

## 优势

1. **完整的数据校验**：每个步骤都有相应的API校验
2. **良好的用户体验**：清晰的错误提示和自动清空机制
3. **数据准确性**：确保只有有效数据才能提交
4. **异常安全**：完善的异常处理机制
5. **可扩展性**：易于添加新的校验规则和业务逻辑
