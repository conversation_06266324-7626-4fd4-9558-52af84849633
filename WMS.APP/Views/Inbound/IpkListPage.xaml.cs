using Microsoft.Maui.Controls;
using WMS.APP.ViewModels.inbound;
using WMS.SERVICE;
using WMS.APP.Common;

namespace WMS.APP.Views.inbound
{
    public partial class IpkListPage : ContentPage
    {
        private readonly IpkListViewModel _viewModel;

        // 构造函数注入服务
        public IpkListPage(IpkListViewModel viewModel)
        {
            InitializeComponent();
            _viewModel = viewModel;
            BindingContext = _viewModel;
        }
    }
}