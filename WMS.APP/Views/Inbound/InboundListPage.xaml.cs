using Microsoft.Maui.Controls;
using WMS.APP.ViewModels.inbound;
using WMS.SERVICE;
using WMS.APP.Common;

namespace WMS.APP.Views.inbound // �޸�Ϊ����ĸ��д��ƥ���ļ��к�XAML
{
    public partial class InboundListPage : ContentPage
    {
        private readonly InboundListViewModel _viewModel;

        // ���캯��ע�����
        public InboundListPage(InboundListViewModel viewModel)
        {
            InitializeComponent();
            //this.BindingContext = new InboundListViewModel(dataService, navigationService);
            _viewModel = viewModel;
            BindingContext = _viewModel;
        }
    }
}