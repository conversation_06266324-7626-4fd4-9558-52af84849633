using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using WMS.APP.Common;
using WMS.APP.Views.inbound;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.inbound
{
    [QueryProperty(nameof(OrderParameter), "orderId")]
    public partial class OrderEditViewModel : ObservableObject
    {
        private readonly INavigationService _navigationService;
        private readonly IInboundService _dataService;

        [ObservableProperty]
        private OrderModel _currentOrder;

        // 添加用于接收参数的属性
        public object OrderParameter
        {
            set
            {
                if (value is OrderModel order)
                {
                    Initialize(order);
                }
            }
        }

        [ObservableProperty]
        private ObservableCollection<string> _statusOptions = new()
        {
            "新建", "部分收货", "完成", "取消"
        };

        public OrderEditViewModel(INavigationService navigationService, IInboundService dataService)
        {
            _navigationService = navigationService;
            _dataService = dataService;
        }

        public void Initialize(OrderModel order)
        {
            CurrentOrder = order;
        }

        [RelayCommand]
        private async Task SaveOrder()
        {
            try
            {
                // 保存订单
                await _dataService.SaveReceiveOrderAsync(CurrentOrder);
                await Shell.Current.DisplayAlert("成功", "订单保存成功", "确定");

                // 返回上一页
                await _navigationService.CloseCurrentAsync();
            }
            catch (Exception ex)
            {
                await Shell.Current.DisplayAlert("错误", $"保存订单失败: {ex.Message}", "确定");
            }
        }

        [RelayCommand]
        private async Task EditDetails()
        {
            // 先保存当前订单
            await _dataService.SaveReceiveOrderAsync(CurrentOrder);

            // 导航到明细编辑页面
            await _navigationService.NavigateToAsync(nameof(ReceiveOrderDetailsPage), CurrentOrder);
        }
    }
}
