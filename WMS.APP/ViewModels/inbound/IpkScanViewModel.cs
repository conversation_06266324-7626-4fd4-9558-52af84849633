using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using Microsoft.Maui.Graphics;
using System.Collections.ObjectModel;
using WMS.APP.Common;
using WMS.APP.Message;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.inbound
{
    [QueryProperty(nameof(OrderId), "orderId")]
    public partial class IpkScanViewModel : ObservableObject
    {
        private readonly IInboundService _dataService;
        private readonly INavigationService _navigationService;

        [ObservableProperty]
        private string orderId;

        [ObservableProperty]
        private OrderModel order;

        [ObservableProperty]
        private string orderNumber;

        [ObservableProperty]
        private string productCode;

        [ObservableProperty]
        private string locationCode;

        [ObservableProperty]
        private string quantity;

        [ObservableProperty]
        private bool isOrderNumberActive = true;

        [ObservableProperty]
        private bool isProductCodeActive = false;

        [ObservableProperty]
        private bool isLocationCodeActive = false;

        [ObservableProperty]
        private bool isQuantityActive = false;

        [ObservableProperty]
        private bool isProductInfoVisible = false;

        [ObservableProperty]
        private OrderDetailModel currentProduct;

        [ObservableProperty]
        private string statusMessage;

        [ObservableProperty]
        private Color statusColor = Colors.Black;

        public IpkScanViewModel(IInboundService dataService, INavigationService navigationService)
        {
            _dataService = dataService;
            _navigationService = navigationService;
        }

        // 当OrderId属性变化时加载订单数据
        partial void OnOrderIdChanged(string value)
        {
            if (!string.IsNullOrEmpty(value))
            {
                _ = LoadOrderAsync(value);
            }
        }

        private async Task LoadOrderAsync(string orderId)
        {
            try
            {
                Order = await _dataService.GetReceiveOrderByIdAsync(orderId);
                if (Order != null)
                {
                    OrderNumber = Order.OrderNumber;
                    StatusMessage = $"订单 {Order.OrderNumber} 已加载";
                    StatusColor = Colors.Green;
                }
                else
                {
                    StatusMessage = "未找到订单";
                    StatusColor = Colors.Red;
                }
            }
            catch (Exception ex)
            {
                StatusMessage = $"加载订单失败: {ex.Message}";
                StatusColor = Colors.Red;
            }
        }

        // 处理扫描结果
        public void HandleScanResult(ScanCompletedMessage message)
        {
            switch (message.FromControlId)
            {
                case "OrderNumber":
                    ValidateOrderNumber(message.InputResult);
                    break;
                case "ProductCode":
                    ValidateProductCode(message.InputResult);
                    break;
                case "LocationCode":
                    ValidateLocationCode(message.InputResult);
                    break;
                case "Quantity":
                    if (message.IsFinalField)
                    {
                        ValidateQuantity(message.InputResult);
                        // 如果是最终字段且自动提交
                        _ = SubmitAsync();
                    }
                    break;
            }
        }

        private void ValidateOrderNumber(string input)
        {
            if (Order != null && Order.OrderNumber == input)
            {
                IsOrderNumberActive = false;
                IsProductCodeActive = true;
                StatusMessage = "请扫描商品条码";
                StatusColor = Colors.Black;
            }
            else
            {
                StatusMessage = "订单号不匹配";
                StatusColor = Colors.Red;
            }
        }

        private void ValidateProductCode(string input)
        {
            if (Order != null)
            {
                var product = Order.Details.FirstOrDefault(d => d.ProductCode == input);
                if (product != null)
                {
                    CurrentProduct = product;
                    IsProductInfoVisible = true;
                    IsProductCodeActive = false;
                    IsLocationCodeActive = true;
                    StatusMessage = $"已识别商品: {product.ProductName}";
                    StatusColor = Colors.Green;
                }
                else
                {
                    StatusMessage = "未找到匹配的商品";
                    StatusColor = Colors.Red;
                }
            }
        }

        private void ValidateLocationCode(string input)
        {
            // 这里可以添加库位验证逻辑
            // 简化版本直接接受任何输入
            IsLocationCodeActive = false;
            IsQuantityActive = true;
            StatusMessage = "请输入拣货数量";
            StatusColor = Colors.Black;
        }

        private void ValidateQuantity(string input)
        {
            if (decimal.TryParse(input, out decimal qty) && qty > 0)
            {
                if (CurrentProduct != null && qty <= CurrentProduct.PlannedQuantity - CurrentProduct.ActualQuantity)
                {
                    StatusMessage = $"数量有效: {qty}";
                    StatusColor = Colors.Green;
                }
                else
                {
                    StatusMessage = "数量超出计划数量";
                    StatusColor = Colors.Red;
                }
            }
            else
            {
                StatusMessage = "请输入有效的数量";
                StatusColor = Colors.Red;
            }
        }

        [RelayCommand]
        private async Task SubmitAsync()
        {
            if (CurrentProduct == null)
            {
                StatusMessage = "请先扫描商品";
                StatusColor = Colors.Red;
                return;
            }

            if (!decimal.TryParse(Quantity, out decimal qty) || qty <= 0)
            {
                StatusMessage = "请输入有效的数量";
                StatusColor = Colors.Red;
                return;
            }

            try
            {
                // 更新商品的实际拣货数量
                CurrentProduct.ActualQuantity += qty;
                
                // 更新订单状态
                if (Order.CompletedItems == Order.TotalItems)
                {
                    Order.Status = "已完成拣货";
                }
                else
                {
                    Order.Status = "部分拣货";
                }

                // 保存订单更新
                await _dataService.SaveReceiveOrderAsync(Order);

                // 重置扫描界面，准备下一次扫描
                ResetScanFields();

                StatusMessage = "拣货成功提交";
                StatusColor = Colors.Green;
            }
            catch (Exception ex)
            {
                StatusMessage = $"提交失败: {ex.Message}";
                StatusColor = Colors.Red;
            }
        }

        private void ResetScanFields()
        {
            ProductCode = string.Empty;
            LocationCode = string.Empty;
            Quantity = string.Empty;
            CurrentProduct = null;
            IsProductInfoVisible = false;
            
            IsOrderNumberActive = false;
            IsProductCodeActive = true;
            IsLocationCodeActive = false;
            IsQuantityActive = false;
        }
    }
}