using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using System.Windows.Input;
using WMS.MODEL;

namespace WMS.APP.ViewModels.inventory
{
    public partial class InvTransferViewModel : ObservableObject
    {
        [ObservableProperty]
        private string sourceLoc = string.Empty;

        [ObservableProperty]
        private string skuCode = string.Empty;

        [ObservableProperty]
        private string targetLoc = string.Empty;

        [ObservableProperty]
        private bool isAllChecked;

        public ObservableCollection<InventoryModel> InventoryListData { get; } = new();

        public ObservableCollection<InventoryModel> InventoryList { get; set; } = new();

        //public IRelayCommand ToggleAllCommand { get; }
        //public IRelayCommand<InventoryModel> IsCheckCommand { get; }
        //public IRelayCommand QueryCommand { get; }
        //public IRelayCommand TransferCommand { get; }

        public InvTransferViewModel()
        {
            // ʾ������
            InventoryListData.Add(new InventoryModel
            {
                invId = 0,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-01",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 1,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-01",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 3,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-01",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 4,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-01",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 5,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-01",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 6,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-01",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 7,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-07",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 8,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-07",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 9,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-07",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 10,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-07",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 11,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-07",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 12,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-07",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 13,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-06",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 14,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-06",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 15,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-06",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 16,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-06",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 17,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-06",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 18,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-05",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 19,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-05",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 20,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-04",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 21,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-04",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 22,
                skuCode = "B67890",
                skuName = "��ƷB",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-04",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 23,
                skuCode = "A12345",
                skuName = "��ƷA",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-03",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 24,
                skuCode = "B67890",
                skuName = "��ƷBUUUUUUUUUUUUUUUUUUUUUUUUUUUU",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-03",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 25,
                skuCode = "A12345",
                skuName = "��ƷAUUUUUUUUUUUUUUUUUUUUUUUUUUUU",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-03",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 26,
                skuCode = "B67890",
                skuName = "��ƷBUUUUUUUUUUUUUUUUUUUUUUUUUUUU",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-02",
                skuUnit = "��",
                skuQty = 10
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 27,
                skuCode = "A12345",
                skuName = "��ƷAUUUUUUUUUUUUUUUUUUUUUUUUUUUU",
                skuLot = "202306",
                skuAtr = "��ɫ/��",
                locCode = "L01-02",
                skuUnit = "��",
                skuQty = 5
            });
            InventoryListData.Add(new InventoryModel
            {
                invId = 28,
                skuCode = "B67890",
                skuName = "��ƷBUUUUUUUUUUUUUUUUUUUUUUUUUUUU",
                skuLot = "202307",
                skuAtr = "��ɫ/С",
                locCode = "L01-02",
                skuUnit = "��",
                skuQty = 10
            });

            //ToggleAllCommand = new RelayCommand(ToggleAll);
            //IsCheckCommand = new RelayCommand<InventoryModel>(OnItemChecked);
            //QueryCommand = new RelayCommand(QueryInventory);
            //TransferCommand = new RelayCommand(Transfer);
        }
        [RelayCommand]
        private void ToggleAll()
        {
            foreach (var item in InventoryList)
                item.IsSelected = IsAllChecked;
        }
        [RelayCommand]
        private void OnItemChecked(InventoryModel? item)
        {
            if (item == null) return;
            // ����չ��������ѡ���߼�
            IsAllChecked = InventoryList.Count > 0 && InventoryList.All(x => x.IsSelected);
        }
        [RelayCommand]
        private void QueryInventory()
        {
            // TODO: ���÷����ѯ��ˢ�� InventoryList
            // ʾ������ղ����������
            InventoryList.Clear();
            //���� sourceLoc �� skuCode ���� InventoryListData
            //ʹ�� LINQ ��ѯ
            var query = InventoryListData.Where(x =>
                (string.IsNullOrEmpty(sourceLoc) || x.locCode == sourceLoc) &&
                (string.IsNullOrEmpty(skuCode) || x.skuCode.Contains(skuCode)));
            //����query ��ֵ �� InventoryList
            foreach (var item in query)
            {
                // ������Ը�����Ҫ�����������������
                InventoryList.Add(new InventoryModel
                {
                    invId = item.invId,
                    skuCode = item.skuCode,
                    skuName = item.skuName,
                    skuAtr = item.skuAtr,
                    locCode = item.locCode,
                    skuLot = item.skuLot,
                    skuUnit = item.skuUnit,
                    skuQty = item.skuQty,
                    IsSelected = false // Ĭ�ϲ�ѡ��
                });
            }
            //InventoryList = query.ToList();
        }
        [RelayCommand]
        private void Transfer()
        {
            // TODO: ʵ���ƿ��߼�
        }
    }
}