using CommunityToolkit.Maui.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Collections.ObjectModel;
using WMS.APP.Views.outbound;
using WMS.MODEL;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.outbound
{
    public partial class PkmOrderViewModel : ObservableObject
    {
        private readonly IOutboundService _outboundService;
        private int _currentPage = 1;
        private const int PageSize = 5;

        [ObservableProperty]
        private bool isBusy;

        [ObservableProperty]
        private bool isRefreshing;

        [ObservableProperty]
        private string systemOrderNo;

        [ObservableProperty]
        private string customerOrderNo;

        [ObservableProperty]
        private DateTime? startDate;

        [ObservableProperty]
        private DateTime? endDate;

        [ObservableProperty]
        private bool hasDateFilter;

        [ObservableProperty]
        private ObservableCollection<PkmOrderModel> _orders;

        public PkmOrderViewModel(IOutboundService outboundService)
        {
            _outboundService = outboundService;
            Orders = new ObservableCollection<PkmOrderModel>();
        }

        [RelayCommand]
        private async Task ShowMoreFilters()
        {
            var popup = new DateFilterPopup(StartDate, EndDate);
            popup.DateSelected += (s, e) =>
            {
                StartDate = e.StartDate;
                EndDate = e.EndDate;
                HasDateFilter = StartDate.HasValue || EndDate.HasValue;
            };
            await Application.Current.MainPage.ShowPopupAsync(popup);
        }

        [RelayCommand]
        private async Task Search()
        {
            IsBusy = true;
            try
            {
                _currentPage = 1;
                Orders.Clear();
                await LoadOrders();
            }
            finally
            {
                IsBusy = false;
            }
        }

        [RelayCommand]
        private async Task Refresh()
        {
            IsRefreshing = true;
            try
            {
                await Search();
            }
            finally
            {
                IsRefreshing = false;
            }
        }

        [RelayCommand]
        private async Task LoadMore()
        {
            if (IsBusy) return;
            IsBusy = true;

            try
            {
                var newOrders = await _outboundService.GetOutboundOrdersAsync(
                    SystemOrderNo,
                    CustomerOrderNo,
                    StartDate,
                    EndDate,
                    ++_currentPage,
                    PageSize);

                if (newOrders?.Any() != true)
                {
                    await Application.Current.MainPage.DisplayAlert("��ʾ", "û�и���������", "ȷ��");
                    return;
                }

                foreach (var order in newOrders)
                {
                    Orders.Add(order);
                }
            }
            catch (Exception ex)
            {
                await Application.Current.MainPage.DisplayAlert("����", ex.Message, "ȷ��");
            }
            finally
            {
                IsBusy = false;
            }
        }

        private async Task LoadOrders()
        {
            var orders = await _outboundService.GetOutboundOrdersAsync(
                SystemOrderNo,
                CustomerOrderNo,
                StartDate,
                EndDate,
                _currentPage,
                PageSize);

            if (orders?.Any() != true)
            {
                await Application.Current.MainPage.DisplayAlert("��ʾ", "δ�ҵ�����", "ȷ��");
                return;
            }

            foreach (var order in orders)
            {
                Orders.Add(order);
            }
        }
    }
}