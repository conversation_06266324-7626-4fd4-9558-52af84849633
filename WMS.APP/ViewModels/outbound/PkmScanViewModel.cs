using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Diagnostics;
using WMS.SERVICE;

namespace WMS.APP.ViewModels.outbound
{
    public partial class PkmScanViewModel : ObservableObject
    {
        [ObservableProperty]
        private string orderNumber;

        [ObservableProperty]
        private string materialCode;

        [ObservableProperty]
        private string batchNumber;

        [ObservableProperty]
        private string orderQuantity;

        // 背景颜色属性用于高亮当前活跃的输入框
        [ObservableProperty]
        private Color orderEntryBackgroundColor = Colors.Transparent;

        [ObservableProperty]
        private Color materialEntryBackgroundColor = Colors.Transparent;

        [ObservableProperty]
        private Color batchEntryBackgroundColor = Colors.Transparent;

        [ObservableProperty]
        private Color quantityEntryBackgroundColor = Colors.Transparent;

        // 添加数量提示属性
        [ObservableProperty]
        private string quantityHint = string.Empty;

        public bool CanSubmit =>
            !string.IsNullOrWhiteSpace(OrderNumber)
            && !string.IsNullOrWhiteSpace(MaterialCode)
            && !string.IsNullOrWhiteSpace(BatchNumber)
            && !string.IsNullOrWhiteSpace(OrderQuantity);

        public IRelayCommand SubmitOrderCommand { get; }

        private string currentActiveControlId; // 当前活跃控件ID跟踪
        private readonly IOutboundService _outboundService;

        public PkmScanViewModel(IOutboundService outboundService)
        {
            _outboundService = outboundService;
            SubmitOrderCommand = new RelayCommand(SubmitOrder, () => CanSubmit);

            this.PropertyChanged += (_, e) =>
            {
                if (e.PropertyName == nameof(OrderNumber) ||
                    e.PropertyName == nameof(MaterialCode) ||
                    e.PropertyName == nameof(BatchNumber) ||
                    e.PropertyName == nameof(OrderQuantity))
                {
                    SubmitOrderCommand.NotifyCanExecuteChanged();
                }
            };
        }

        public void InitializeEntries()
        {
            HighlightNextEmpty();
        }

        /// <summary>
        /// 根据字段ID设置活跃的Entry
        /// </summary>
        public void SetActiveEntryByFieldId(string fieldId)
        {
            currentActiveControlId = fieldId;

            // 重置所有背景颜色
            OrderEntryBackgroundColor = Colors.Transparent;
            MaterialEntryBackgroundColor = Colors.Transparent;
            BatchEntryBackgroundColor = Colors.Transparent;
            QuantityEntryBackgroundColor = Colors.Transparent;

            // 设置当前活跃的Entry高亮
            switch (fieldId)
            {
                case "OrderEntry":
                    OrderEntryBackgroundColor = Colors.Yellow;
                    break;
                case "MaterialEntry":
                    MaterialEntryBackgroundColor = Colors.Yellow;
                    break;
                case "BatchEntry":
                    BatchEntryBackgroundColor = Colors.Yellow;
                    break;
                case "QuantityEntry":
                    QuantityEntryBackgroundColor = Colors.Yellow;
                    break;
            }
        }

        public void HandleScanInput(string result)
        {
            if (string.IsNullOrEmpty(OrderNumber)) OrderNumber = result;
            else if (string.IsNullOrEmpty(MaterialCode)) MaterialCode = result;
            else if (string.IsNullOrEmpty(BatchNumber)) BatchNumber = result;

            HighlightNextEmpty(); // 跳转但不提交
        }
        public async void HandleScanInputByFieldId(string fieldId, string result)
        {
            // 记录当前活跃的控件ID
            currentActiveControlId = fieldId;
            Debug.WriteLine($"Current load FieldId: {currentActiveControlId}");

            switch (fieldId)
            {
                case "OrderEntry":
                    await HandleOrderNumberInput(result);
                    break;
                case "MaterialEntry":
                    await HandleMaterialCodeInput(result);
                    break;
                case "BatchEntry":
                    await HandleBatchNumberInput(result);
                    break;
                case "QuantityEntry":
                    OrderQuantity = result;
                    break;
            }
        }

        private async Task HandleOrderNumberInput(string orderNumber)
        {
            if (string.IsNullOrWhiteSpace(orderNumber))
            {
                await ShowErrorAndClear("订单号不能为空", "OrderEntry");
                return;
            }

            try
            {
                bool isValid = await _outboundService.ValidateOrderNumberAsync(orderNumber);
                if (isValid)
                {
                    OrderNumber = orderNumber;
                    HighlightNextEmpty(); // 跳转到下一个字段
                }
                else
                {
                    await ShowErrorAndClear("单号不存在", "OrderEntry");
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAndClear($"校验失败: {ex.Message}", "OrderEntry");
            }
        }

        private async Task HandleMaterialCodeInput(string materialCode)
        {
            if (string.IsNullOrWhiteSpace(OrderNumber))
            {
                await ShowErrorAndClear("请先扫描订单号", "MaterialEntry");
                return;
            }

            if (string.IsNullOrWhiteSpace(materialCode))
            {
                await ShowErrorAndClear("SKU不能为空", "MaterialEntry");
                return;
            }

            try
            {
                bool orderExists = await _outboundService.ValidateOrderNumberAsync(OrderNumber);
                if (!orderExists)
                {
                    await ShowErrorAndClear("单号不存在", "MaterialEntry");
                    return;
                }

                bool skuExists = await _outboundService.ValidateSkuCodeAsync(OrderNumber, materialCode);
                if (skuExists)
                {
                    MaterialCode = materialCode;
                    HighlightNextEmpty(); // 跳转到下一个字段
                }
                else
                {
                    await ShowErrorAndClear("SKU不存在", "MaterialEntry");
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAndClear($"校验失败: {ex.Message}", "MaterialEntry");
            }
        }

        private async Task HandleBatchNumberInput(string batchNumber)
        {
            if (string.IsNullOrWhiteSpace(OrderNumber))
            {
                await ShowErrorAndClear("请先扫描订单号", "BatchEntry");
                return;
            }

            if (string.IsNullOrWhiteSpace(MaterialCode))
            {
                await ShowErrorAndClear("请先扫描SKU", "BatchEntry");
                return;
            }

            if (string.IsNullOrWhiteSpace(batchNumber))
            {
                await ShowErrorAndClear("批次号不能为空", "BatchEntry");
                return;
            }

            try
            {
                bool batchExists = await _outboundService.ValidateBatchNumberAsync(OrderNumber, MaterialCode, batchNumber);
                if (batchExists)
                {
                    BatchNumber = batchNumber;

                    // 获取并显示需要拣货的数量
                    decimal requiredQuantity = await _outboundService.GetRequiredQuantityAsync(OrderNumber, MaterialCode, batchNumber);
                    QuantityHint = $"需要拣货数量: {requiredQuantity}";

                    HighlightNextEmpty(); // 跳转到数量字段
                }
                else
                {
                    await ShowErrorAndClear("批次号不存在", "BatchEntry");
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAndClear($"校验失败: {ex.Message}", "BatchEntry");
            }
        }

        private async Task ShowErrorAndClear(string message, string fieldId)
        {
            // 显示错误提示
            await Application.Current?.MainPage?.DisplayAlert("提示", message, "确定");

            // 等待2秒后清空当前字段
            await Task.Delay(2000);

            // 清空当前扫描或输入结果
            switch (fieldId)
            {
                case "OrderEntry":
                    OrderNumber = string.Empty;
                    break;
                case "MaterialEntry":
                    MaterialCode = string.Empty;
                    break;
                case "BatchEntry":
                    BatchNumber = string.Empty;
                    break;
                case "QuantityEntry":
                    OrderQuantity = string.Empty;
                    break;
            }

            // 不自动跳转到下一个控件，保持当前高亮
        }

        public void HandleManualCompleted()
        {
            if (CanSubmit)
            {
                SubmitOrder();
            }
            else
            {
                HighlightNextEmpty();
            }
        }

        private void HighlightNextEmpty()
        {
            // 重置所有背景颜色
            OrderEntryBackgroundColor = Colors.Transparent;
            MaterialEntryBackgroundColor = Colors.Transparent;
            BatchEntryBackgroundColor = Colors.Transparent;
            QuantityEntryBackgroundColor = Colors.Transparent;

            // 改进焦点管理逻辑
            // 如果有当前活跃控件，尝试从当前控件的下一个开始查找
            if (!string.IsNullOrWhiteSpace(currentActiveControlId))
            {
                var currentIndex = GetControlIndex(currentActiveControlId);

                // 从当前控件的下一个开始查找空字段
                for (int i = currentIndex; i < 4; i++)
                {
                    if (IsControlEmpty(i))
                    {
                        SetActiveControl(i);
                        return;
                    }
                }

                // 如果后面没有空字段，从头开始查找
                for (int i = 0; i <= currentIndex + 1; i++)
                {
                    if (IsControlEmpty(i))
                    {
                        SetActiveControl(i);
                        return;
                    }
                }
            }
            else
            {
                // 默认逻辑：从第一个空字段开始
                if (string.IsNullOrWhiteSpace(OrderNumber)) SetActiveControl(0);
                else if (string.IsNullOrWhiteSpace(MaterialCode)) SetActiveControl(1);
                else if (string.IsNullOrWhiteSpace(BatchNumber)) SetActiveControl(2);
                else if (string.IsNullOrWhiteSpace(OrderQuantity)) SetActiveControl(3);
            }
        }

        private int GetControlIndex(string controlId)
        {
            return controlId switch
            {
                "OrderEntry" => 0,
                "MaterialEntry" => 1,
                "BatchEntry" => 2,
                "QuantityEntry" => 3,
                _ => 0
            };
        }

        private bool IsControlEmpty(int index)
        {
            return index switch
            {
                0 => string.IsNullOrWhiteSpace(OrderNumber),
                1 => string.IsNullOrWhiteSpace(MaterialCode),
                2 => string.IsNullOrWhiteSpace(BatchNumber),
                3 => string.IsNullOrWhiteSpace(OrderQuantity),
                _ => false
            };
        }

        private void SetActiveControl(int index)
        {
            switch (index)
            {
                case 0:
                    OrderEntryBackgroundColor = Colors.Yellow;
                    break;
                case 1:
                    MaterialEntryBackgroundColor = Colors.Yellow;
                    break;
                case 2:
                    BatchEntryBackgroundColor = Colors.Yellow;
                    break;
                case 3:
                    QuantityEntryBackgroundColor = Colors.Yellow;
                    break;
            }
        }

        private async void SubmitOrder()
        {
            if (!decimal.TryParse(OrderQuantity, out decimal quantity) || quantity <= 0)
            {
                await ShowErrorAndClear("请输入有效的数量", "QuantityEntry");
                return;
            }

            try
            {
                bool success = await _outboundService.SubmitPickingDataAsync(OrderNumber, MaterialCode, BatchNumber, quantity);
                if (success)
                {
                    await Application.Current?.MainPage?.DisplayAlert("成功", "订单提交成功", "OK");

                    // 清空所有字段
                    OrderNumber = MaterialCode = BatchNumber = OrderQuantity = string.Empty;
                    QuantityHint = string.Empty;
                    currentActiveControlId = null; // 重置当前活跃控件ID

                    HighlightNextEmpty();
                }
                else
                {
                    await ShowErrorAndClear("提交失败，请检查数据", "QuantityEntry");
                }
            }
            catch (Exception ex)
            {
                await ShowErrorAndClear($"提交失败: {ex.Message}", "QuantityEntry");
            }
        }
    }
}
