using Microsoft.Maui.Controls.PlatformConfiguration;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WMS.APP.Utils
{
    public class ScanFormManager
    {
        private readonly Dictionary<string, Entry> _fieldMap = new();
        private readonly List<string> _fieldOrder = new();
        private string _activeField;

        public event Action OnFormCompleted;

        public void RegisterField(string fieldId, Entry entry)
        {
            _fieldMap[fieldId] = entry;
            _fieldOrder.Add(fieldId);

            // 高亮点击激活
            var tap = new TapGestureRecognizer();
            tap.Tapped += (s, e) => ActivateField(fieldId);
            entry.GestureRecognizers.Add(tap);
        }

        public void SetInitialField(string fieldId)
        {
            _activeField = fieldId;
            UpdateHighlight();
        }

        public void HandleCompleted(string value)
        {
            if (!string.IsNullOrEmpty(_activeField) && _fieldMap.TryGetValue(_activeField, out var entry))
            {
                entry.Text = value;
                entry.Unfocus(); // 取消焦点，关闭光标 & 键盘
            }


            var index = _fieldOrder.IndexOf(_activeField);
            if (index < _fieldOrder.Count - 1)
            {
                _activeField = _fieldOrder[index + 1];
                UpdateHighlight();
            }
            else
            {
                OnFormCompleted?.Invoke();
            }
        }

        public void ActivateField(string fieldId)
        {
            if (_fieldMap.ContainsKey(fieldId))
            {
                _activeField = fieldId;
                UpdateHighlight();
            }
        }

        private void UpdateHighlight()
        {
            foreach (var pair in _fieldMap)
            {
                pair.Value.BackgroundColor = pair.Key == _activeField ? Colors.Yellow : Colors.Transparent;
            }
        }
    }
}
