
using System.Diagnostics;

namespace WMS.APP
{
    public partial class App : Application
    {
        public static IServiceProvider? ServiceProvider { get; private set; }

        private bool _isExiting = false;

        //public static IScannerService ScannerService { get; private set; }

        public App(IServiceProvider serviceProvider)
        {
            InitializeComponent();
            ServiceProvider = serviceProvider;
            //ScannerService = scanner;
            MainPage = new AppShell();
        }

        // 处理应用退出逻辑
        public async Task<bool> ConfirmExit()
        {
            if (_isExiting) return true;

            _isExiting = true;
            bool confirm = await MainPage.DisplayAlert("退出提示",
                "确定要退出应用吗？", "退出", "取消");
            _isExiting = false;

            return confirm;
        }

        // App.xaml.cs
        protected override void OnStart()
        {
            AppDomain.CurrentDomain.FirstChanceException += (sender, e) =>
            {
                Debug.WriteLine($"异常类型: {e.Exception.GetType()}");
                Debug.WriteLine($"异常信息: {e.Exception.Message}");
                Debug.WriteLine($"堆栈跟踪: {e.Exception.StackTrace}");
            };
        }

        protected override Window CreateWindow(IActivationState? activationState)
        {
            var window = base.CreateWindow(activationState);
            return window;
        }

        // App.xaml.cs

        public async Task GoToLoginPage()
        {
            MainPage = new AppShell(); // 或者直接 new LoginPage()
            //移除用户信息
            Preferences.Remove("UserName");
            Preferences.Remove("Password");
            await Shell.Current.GoToAsync("//LoginPage");
        }
    }
}
