using WMS.APP.Common;
#if ANDROID
using Android.Views;
#endif
namespace WMS.APP;


public partial class BasePage : ContentPage
{
    protected override void OnAppearing()
    {
        base.OnAppearing();
        GlobalFocusManager.Instance.RegisterPage(this);
        GlobalFocusManager.Instance.InitializePage();
    }

#if ANDROID
    protected override void OnHandlerChanged()
    {
        base.OnHandlerChanged();

        if (GlobalFocusManager.Instance.IsAndroid7() &&
            Handler?.PlatformView != null)
        {
            var view = (Handler.PlatformView as Android.Views.View);
            view?.ViewTreeObserver.AddOnGlobalLayoutListener(
                new GlobalLayoutListener(this));
        }
    }

    private class GlobalLayoutListener : Java.Lang.Object,
        ViewTreeObserver.IOnGlobalLayoutListener
    {
        private readonly WeakReference<BasePage> _pageRef;

        public GlobalLayoutListener(BasePage page) =>
            _pageRef = new WeakReference<BasePage>(page);

        public void OnGlobalLayout()
        {
            if (!_pageRef.TryGetTarget(out var page)) return;
            if (page.Handler?.MauiContext?.Context == null) return;

            if (page.Handler.PlatformView is Android.Views.View androidView)
            {
                androidView.Post(() => Device.BeginInvokeOnMainThread(ResetFocus));
            }

            void ResetFocus()
            {
                try
                {
                    var focusManager = GlobalFocusManager.Instance;
                    /*var first = focusManager.FindFirstFocusable(page.Content);

                    if (first == null) return;

                    first.Focus();
                    first.Unfocus();*/
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"焦点重置错误: {ex.Message}");
                }
            }
        }
    }
#endif
}