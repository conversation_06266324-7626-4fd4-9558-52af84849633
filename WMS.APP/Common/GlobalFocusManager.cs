using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WMS.APP.Common
{
    public class GlobalFocusManager : IFocusManager
    {
        private static readonly Lazy<GlobalFocusManager> _instance =
            new Lazy<GlobalFocusManager>(() => new GlobalFocusManager());

        public static IFocusManager Instance => _instance.Value;

        private ContentPage _currentPage;
        private bool _isFirstLoad = true;

        private GlobalFocusManager() { }

        public void RegisterPage(ContentPage page)
        {
            _currentPage = page;
        }

        public void InitializePage()
        {
            if (!IsAndroid7()) return;

            Device.BeginInvokeOnMainThread(async () =>
            {
                // 获取页面第一个可聚焦元素
                var firstFocusable = FindFirstFocusable(_currentPage.Content);
                if (firstFocusable != null)
                {
                    firstFocusable.Focus();
                    await Task.Delay(100);
                    firstFocusable.Unfocus();
                }
            });
        }

        public async void HandleEntryCompleted(Entry entry, Action nextFieldAction)
        {
            if (!IsAndroid7())
            {
                nextFieldAction?.Invoke();
                return;
            }

            // Android 7.x 专用处理
            entry.Unfocus();
            await Task.Delay(50);

            // 执行原定的下一步操作
            nextFieldAction?.Invoke();
        }

        public View FindFirstFocusable(Layout layout)
        {
            // 递归查找第一个可聚焦控件
            foreach (var child in layout.Children)
            {
                if (child is Entry entry && entry.IsVisible && entry.IsEnabled)
                    return entry;

                if (child is Layout childLayout)
                {
                    var result = FindFirstFocusable(childLayout);
                    if (result != null) return result;
                }
            }
            return null;
        }

        public View FindFirstFocusable(View view)
        {
            if (view == null) return null;

            // 处理所有可能的容器类型
            switch (view)
            {
                // 直接处理Entry控件
                case Entry entry when entry.IsVisible && entry.IsEnabled:
                    return entry;

                // 处理布局容器
                case Layout layout:
                    foreach (var child in layout.Children)
                    {
                        var result = FindFirstFocusable(child as View);
                        if (result != null) return result;
                    }
                    break;

                // 处理ContentView类型
                case ContentView contentView:
                    return FindFirstFocusable(contentView.Content as View);

                // 处理其他类型的容器
                case ScrollView scrollView:
                    return FindFirstFocusable(scrollView.Content as View);

                case TemplatedView templatedView:
                    // 可能需要自定义处理模板视图
                    break;
            }

            return null;
        }

        public bool IsAndroid7() =>
            DeviceInfo.Platform == DevicePlatform.Android &&
            DeviceInfo.Version.Major == 7;
    }
}
