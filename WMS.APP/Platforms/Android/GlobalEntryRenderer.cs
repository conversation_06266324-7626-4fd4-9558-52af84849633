using Android.Content;
using Android.OS;
using Android.Views.InputMethods;
using Microsoft.Maui.Controls.Compatibility;
using Microsoft.Maui.Controls.Compatibility.Platform.Android;
using Microsoft.Maui.Controls.Platform;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WMS.APP.Platforms.Android;

[assembly: ExportRenderer(typeof(Entry), typeof(GlobalEntryRenderer))]
namespace WMS.APP.Platforms.Android
{
    public class GlobalEntryRenderer : EntryRenderer
    {
        public GlobalEntryRenderer(Context context) : base(context) { }

        protected override void OnElementChanged(ElementChangedEventArgs<Entry> e)
        {
            base.OnElementChanged(e);

            if (Control == null) return;

            if (Build.VERSION.SdkInt == BuildVersionCodes.N)
            {
                // Android 7.0 专有修复
                Control.ImeOptions = ImeAction.Done;

                Control.EditorAction += (sender, args) =>
                {
                    if (args.ActionId == ImeAction.Done)
                    {
                        args.Handled = true; // 全局拦截
                        Device.BeginInvokeOnMainThread(() =>
                        {
                            (Element as Entry)?.SendCompleted();
                        });
                    }
                };
            }
        }
    }
}
