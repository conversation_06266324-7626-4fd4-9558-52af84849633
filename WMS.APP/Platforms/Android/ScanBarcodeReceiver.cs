using Android.App;
using Android.Content;
using CommunityToolkit.Mvvm.Messaging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WMS.APP.Message;

namespace WMS.APP.Platforms.Android
{
    [BroadcastReceiver(Enabled = true, Exported = true)]
    //[IntentFilter(new[] {
    //   "com.android.server.scannerservice.broadcast", // 通用 PDA 扫码 Action
    //   "com.honeywell.decode.intent.action.EDIT_DATA", // 霍尼韦尔设备
    //   "com.zebra.scanner.ACTION", // 斑马设备
    //   "android.intent.ACTION_DECODE_DATA",//U博讯设备
    //   "nlscan.action.intent.SCANNER_RESULT"//新大陆
    //})] // 根据优博讯设备的广播 action 设置
    public class ScanBarcodeReceiver : BroadcastReceiver
    {

        private readonly Action<string> _onScanReceived;

        public ScanBarcodeReceiver()
        {

        }

        public ScanBarcodeReceiver(Action<string> onScanReceived)
        {
            _onScanReceived = onScanReceived ?? throw new ArgumentNullException(nameof(onScanReceived));
        }

        public override void OnReceive(Context context, Intent intent)
        {
            //var scanData = intent.GetStringExtra("value"); // 根据优博讯设备的广播数据 key 设置
            // 不同设备的数据字段可能不同
          var scanData = intent?.GetStringExtra("barcode")
                        ?? intent?.GetStringExtra("SCAN_BARCODE")
                        ?? intent?.GetStringExtra("com.symbol.datawedge.data_string")
                        ?? intent?.GetStringExtra("barcode_string")
                        ?? intent?.GetStringExtra("SCAN_BARCODE1");

            if (!string.IsNullOrEmpty(scanData))
            {
                System.Diagnostics.Debug.WriteLine($"[扫描结果] {scanData}");
                //Console.WriteLine(scanData);
                //通过信使-发送扫描结果
                WeakReferenceMessenger.Default.Send(new ScannerResultMessage(scanData));
            }

        }
    }
}
