using Android.Views;
using Android.Widget;
using Microsoft.Maui.Handlers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WMS.APP.Platforms.Android
{
    public static class KeyboardWatcher
    {
        public static void Init()
        {
            EntryHandler.Mapper.AppendToMapping("HideCursorOnKeyboardClose", (handler, view) =>
            {
                if (handler.PlatformView is EditText nativeEditText)
                {
                    nativeEditText.SetCursorVisible(false); // 默认隐藏光标

                    /*nativeEditText.Touch += (s, e) =>
                    {
                        if (e.Event.Action == MotionEventActions.Down)
                        {
                            nativeEditText.SetCursorVisible(true);
                        }
                    };

                    nativeEditText.FocusChange += (s, e) =>
                    {
                        if (!nativeEditText.HasFocus)
                            nativeEditText.SetCursorVisible(false);
                    };*/

                    // 键盘关闭时隐藏光标
                    var activity = Platform.CurrentActivity;
                    var rootView = activity?.Window?.DecorView?.RootView;

                    if (rootView != null)
                    {
                        int previousHeight = 0;
                        rootView.ViewTreeObserver?.AddOnGlobalLayoutListener(new GlobalLayoutListener(() =>
                        {
                            Rect r = new();
                            rootView.GetWindowVisibleDisplayFrame(r);
                            int heightDiff = rootView.Height - ((int)r.Height);

                            bool isKeyboardShown = heightDiff > rootView.Height * 0.15; // 判断是否显示键盘

                            if (!isKeyboardShown && previousHeight != 0)
                            {
                                nativeEditText.SetCursorVisible(false);
                            }

                            previousHeight = heightDiff;
                        }));
                    }
                }
            });
        }

        private class GlobalLayoutListener : Java.Lang.Object, ViewTreeObserver.IOnGlobalLayoutListener
        {
            private readonly Action _onGlobalLayout;

            public GlobalLayoutListener(Action onGlobalLayout)
            {
                _onGlobalLayout = onGlobalLayout;
            }

            public void OnGlobalLayout() => _onGlobalLayout.Invoke();
        }
    }
}
