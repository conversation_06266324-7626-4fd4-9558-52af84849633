using Android.Content;
using Android.Runtime;
using Android.Util;
using Android.Views;
using Android.Widget;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WMS.APP.Platforms.Android
{
    public class NoJumpEditText : EditText
    {
        public NoJumpEditText(Context context) : base(context) { }
        public NoJumpEditText(Context context, IAttributeSet attrs) : base(context, attrs) { }

        public override bool OnKeyPreIme([GeneratedEnum] Keycode keyCode, KeyEvent e)
        {
            // 阻止 IME 回车引发的焦点跳转
            if (keyCode == Keycode.Enter)
            {
                // 只触发输入回车事件，不跳焦点
                return true;
            }
            return base.OnKeyPreIme(keyCode, e);
        }

        public override bool OnKeyDown([GeneratedEnum] Keycode keyCode, KeyEvent e)
        {
            if (keyCode == Keycode.Enter)
            {
                // 拦截 Enter 按键（禁止系统默认跳焦点）
                return true;
            }
            return base.OnKeyDown(keyCode, e);
        }
    }
}
