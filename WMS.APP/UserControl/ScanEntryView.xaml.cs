using CommunityToolkit.Mvvm.Messaging;
using System.Diagnostics;
using System.Runtime.Versioning;
using WMS.APP.Message;

namespace WMS.APP.UserControl;

public partial class ScanEntryView : ContentView
{
    public ScanEntryView()
    {
        InitializeComponent();

        // ���������ؼ��Ľ�����Ϣ
        WeakReferenceMessenger.Default.Register<ScanEntryFocusMessage>(this, (r, m) =>
        {
            if (m.ActiveControl != this)
            {
                Debug.WriteLine($"�յ���Ϣ��{m.ActiveControl.ControlId}");
                IsActive = false;
            }
        });
    }

    public static readonly BindableProperty TitleProperty =
        BindableProperty.Create(nameof(Title), typeof(string), typeof(ScanEntryView), string.Empty);

    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    public static readonly BindableProperty ScanTextProperty =
        BindableProperty.Create(nameof(ScanText), typeof(string), typeof(ScanEntryView), string.Empty, BindingMode.TwoWay);

    public string ScanText
    {
        get => (string)GetValue(ScanTextProperty);
        set => SetValue(ScanTextProperty, value);
    }

    public static readonly BindableProperty IsActiveProperty =
        BindableProperty.Create(nameof(IsActive), typeof(bool), typeof(ScanEntryView), false, propertyChanged: OnIsActiveChanged);

    public bool IsActive
    {
        get => (bool)GetValue(IsActiveProperty);
        set => SetValue(IsActiveProperty, value);
    }

    public static readonly BindableProperty IsFinalManualEntryProperty =
        BindableProperty.Create(nameof(IsFinalManualEntry), typeof(bool), typeof(ScanEntryView), false);

    public bool IsFinalManualEntry
    {
        get => (bool)GetValue(IsFinalManualEntryProperty);
        set => SetValue(IsFinalManualEntryProperty, value);
    }

    public string ControlId { get; set; }

    private static void OnIsActiveChanged(BindableObject bindable, object oldVal, object newVal)
    {
        var control = (ScanEntryView)bindable;
        if ((bool)newVal)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                //control.FocusEntry();
                control.entry.BackgroundColor = Colors.Yellow;
                //control.entry.Unfocus();
                //control.entry.CursorPosition = -1;
                if (control.IsFinalManualEntry) // Use the instance reference 'control'  
                {
                    control.entry.Keyboard = Keyboard.Numeric;
                    // Add logic here if needed  
                }
            });
        }
        else
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                control.entry.BackgroundColor = Colors.Transparent;
                //control.entry.Unfocus();
                //control.entry.CursorPosition = -1;
                //control.FocusEntry();
            });
        }
    }

    public void FocusEntry() => entry.Unfocus();

    private async void Entry_Completed(object sender, EventArgs e)
    {
        /*if (sender is Entry entry)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                //entry.IsEnabled = false;
                //entry.IsEnabled = true;
                //entry.IsReadOnly = true;
                //entry.Unfocus(); // ȡ�����㣬�رչ�� & ����
            });
        }*/
        await Task.Delay(300);

        if (!String.IsNullOrEmpty(ScanText))
        {
            WeakReferenceMessenger.Default.Send(new ScanCompletedMessage(ControlId, IsFinalManualEntry, ScanText));
        }
        if (string.IsNullOrEmpty(ScanText))
        {
            if (!IsActive)
            {
                // �������Լ�Ϊ active���ٹ㲥
                IsActive = true;
                WeakReferenceMessenger.Default.Send(new ScanEntryFocusMessage(this));
            }
        }
    }

    // MainPage.xaml.cs
    private void OnEntryFocused(object sender, FocusEventArgs e)
    {
        // ������Ϣ֪ͨ�����ؼ�
        WeakReferenceMessenger.Default.Send(new ScanEntryFocusMessage(this));
        IsActive = true;
    }

    private void OnEntryUnfocused(object sender, FocusEventArgs e)
    {
        // ֻ���ڲ�����Ϊ�����ؼ���ý��������������²�����Ϊfalse
        if (!WeakReferenceMessenger.Default.IsRegistered<ScanEntryFocusMessage>(this))
        {
            IsActive = false;
        }
    }
}