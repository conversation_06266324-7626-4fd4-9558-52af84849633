using WMS.APP.ViewModels;


namespace WMS.APP
{
    public partial class InvMenuPage : ContentPage
    {
        private readonly MenuViewModel _viewModel;

        public InvMenuPage(MenuViewModel viewModel)
        {
            InitializeComponent();
            //_serviceProvider = serviceProvider;
            //_navigation = navigationService;
            _viewModel = viewModel;
            _viewModel.MenuType = "INV";
            _viewModel.initMenu();
            BindingContext = _viewModel;
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
        }
    }
}
