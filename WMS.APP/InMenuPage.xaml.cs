using WMS.APP.ViewModels;


namespace WMS.APP
{
    public partial class InMenuPage : ContentPage
    {
        private readonly MenuViewModel _viewModel;

        public InMenuPage(MenuViewModel viewModel)
        {
            InitializeComponent();
            //_serviceProvider = serviceProvider;
            //_navigation = navigationService;
            _viewModel = viewModel;
            _viewModel.MenuType = "IN";
            _viewModel.initMenu();
            BindingContext = _viewModel;
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();
        }
    }
}
