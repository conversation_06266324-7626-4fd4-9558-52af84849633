using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WMS.MODEL
{
    public class InventoryModel : INotifyPropertyChanged
    {
        public decimal? invId { get; set; }

        public string skuCode { get; set; } = string.Empty; // 产品编码

        public string skuName { get; set; } = string.Empty; // 产品名称

        public string skuAtr { get; set; } = string.Empty; // 产品属性

        public string locCode { get; set; } = string.Empty; // 库位编码

        public string skuLot { get; set; } = string.Empty; // 产品批次

        public string skuUnit { get; set; } // 产品单位

        public int skuQty { get; set; } // 商品数量

        //public bool IsSelected { get; set; } // 是否选中

        private bool _isSelected;
        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged(nameof(IsSelected));
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        protected void OnPropertyChanged(string propertyName)
            => PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));

    }
}
