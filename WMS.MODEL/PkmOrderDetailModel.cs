using System;

namespace WMS.MODEL
{
    public class PkmOrderDetailModel
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string OrderNo { get; set; }
        public string SkuCode { get; set; }
        public string SkuName { get; set; }
        public string BatchNumber { get; set; }
        public decimal RequiredQuantity { get; set; }
        public decimal PickedQuantity { get; set; }
        public string Unit { get; set; } = "个";
        public string Status { get; set; } = "待拣货";
        public DateTime CreateDate { get; set; } = DateTime.Now;
    }
}
