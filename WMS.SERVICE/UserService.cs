using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace WMS.SERVICE
{
    public class UserService : IUserService
    {
        public Task<string> GetUserInfoAsync(string username, string password)
        {
            string result = "";
            if (username == "admin" && password == "123456")
            {
                result = "SUCCESS";
            }
            else
            {
                result = "FAILURE";
            }
            return Task.FromResult(result);
            //throw new NotImplementedException();
        }
    }
}
