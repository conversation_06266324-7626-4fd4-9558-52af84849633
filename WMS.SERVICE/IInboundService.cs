using WMS.MODEL;

namespace WMS.SERVICE
{
    public interface IInboundService
    {

        // 收货单相关方法
        Task<List<OrderModel>> GetReceiveOrdersAsync(string keyword = null);
        Task<OrderModel> GetReceiveOrderByIdAsync(string id);
        Task SaveReceiveOrderAsync(OrderModel order);
        Task DeleteReceiveOrderAsync(string id);

        // 清理临时存储
        Task ClearReceiveOrdersTempStorageAsync();

        // 添加分页获取订单的方法
        Task<List<OrderModel>> GetReceiveOrdersPagedAsync(string keyword, int page, int pageSize);
    }
}
