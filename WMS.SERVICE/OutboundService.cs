using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WMS.MODEL;

namespace WMS.SERVICE
{
    public class OutboundService : IOutboundService
    {
        private readonly List<PkmOrderModel> _mockOrders;
        private readonly List<PkmOrderDetailModel> _mockOrderDetails;
        private readonly Random _random = new Random();

        public OutboundService()
        {
            _mockOrders = GenerateMockOrders();
            _mockOrderDetails = GenerateMockOrderDetails();
        }

        public async Task<List<PkmOrderModel>> GetOutboundOrdersAsync(
            string systemOrderNo = null,
            string customerOrderNo = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int page = 1,
            int pageSize = 10)
        {
            // 模拟网络延迟
            await Task.Delay(800);

            var query = _mockOrders.AsQueryable();

            // 应用筛选条件
            if (!string.IsNullOrWhiteSpace(systemOrderNo))
                query = query.Where(o => o.orderNo.Contains(systemOrderNo));

            if (!string.IsNullOrWhiteSpace(customerOrderNo))
                query = query.Where(o => o.orderCon.Contains(customerOrderNo));

            if (startDate.HasValue)
                query = query.Where(o => o.orderDate >= startDate.Value);

            if (endDate.HasValue)
                query = query.Where(o => o.orderDate <= endDate.Value);

            // 分页
            return query
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        private List<PkmOrderModel> GenerateMockOrders()
        {
            var orders = new List<PkmOrderModel>();
            var orderTypes = new[] { "正常出库", "退货出库", "调拨出库" };
            var owners = new[] { "苹果公司", "华为公司", "小米公司", "OPPO公司", "VIVO公司" };

            for (int i = 1; i <= 50; i++)
            {
                var order = new PkmOrderModel
                {
                    orderNo = $"SO{DateTime.Now:yyyyMMdd}{i:D4}",
                    orderCon = $"CSO{_random.Next(10000, 99999)}",
                    orderType = orderTypes[_random.Next(orderTypes.Length)],
                    owner = owners[_random.Next(owners.Length)],
                    orderDate = DateTime.Now.AddDays(-_random.Next(0, 30)),
                    orderRemark = $"这是第{i}个测试订单的备注信息"
                };
                orders.Add(order);
            }

            return orders.OrderByDescending(o => o.orderDate).ToList();
        }

        // 校验订单号是否存在
        public async Task<bool> ValidateOrderNumberAsync(string orderNumber)
        {
            await Task.Delay(300); // 模拟网络延迟
            return _mockOrders.Any(o => o.orderNo.Equals(orderNumber, StringComparison.OrdinalIgnoreCase));
        }

        // 校验SKU是否存在于指定订单中
        public async Task<bool> ValidateSkuCodeAsync(string orderNumber, string skuCode)
        {
            await Task.Delay(300); // 模拟网络延迟
            return _mockOrderDetails.Any(d =>
                d.OrderNo.Equals(orderNumber, StringComparison.OrdinalIgnoreCase) &&
                d.SkuCode.Equals(skuCode, StringComparison.OrdinalIgnoreCase));
        }

        // 校验批次号是否存在于指定订单和SKU中
        public async Task<bool> ValidateBatchNumberAsync(string orderNumber, string skuCode, string batchNumber)
        {
            await Task.Delay(300); // 模拟网络延迟
            return _mockOrderDetails.Any(d =>
                d.OrderNo.Equals(orderNumber, StringComparison.OrdinalIgnoreCase) &&
                d.SkuCode.Equals(skuCode, StringComparison.OrdinalIgnoreCase) &&
                d.BatchNumber.Equals(batchNumber, StringComparison.OrdinalIgnoreCase));
        }

        // 获取需要拣货的数量
        public async Task<decimal> GetRequiredQuantityAsync(string orderNumber, string skuCode, string batchNumber)
        {
            await Task.Delay(200); // 模拟网络延迟
            var detail = _mockOrderDetails.FirstOrDefault(d =>
                d.OrderNo.Equals(orderNumber, StringComparison.OrdinalIgnoreCase) &&
                d.SkuCode.Equals(skuCode, StringComparison.OrdinalIgnoreCase) &&
                d.BatchNumber.Equals(batchNumber, StringComparison.OrdinalIgnoreCase));

            return detail?.RequiredQuantity ?? 0;
        }

        // 提交拣货数据
        public async Task<bool> SubmitPickingDataAsync(string orderNumber, string skuCode, string batchNumber, decimal quantity)
        {
            await Task.Delay(500); // 模拟网络延迟

            var detail = _mockOrderDetails.FirstOrDefault(d =>
                d.OrderNo.Equals(orderNumber, StringComparison.OrdinalIgnoreCase) &&
                d.SkuCode.Equals(skuCode, StringComparison.OrdinalIgnoreCase) &&
                d.BatchNumber.Equals(batchNumber, StringComparison.OrdinalIgnoreCase));

            if (detail != null)
            {
                detail.PickedQuantity += quantity;
                detail.Status = detail.PickedQuantity >= detail.RequiredQuantity ? "已完成" : "部分拣货";
                return true;
            }

            return false;
        }

        // 生成模拟订单详情数据
        private List<PkmOrderDetailModel> GenerateMockOrderDetails()
        {
            var details = new List<PkmOrderDetailModel>();
            var skuCodes = new[] { "SKU001", "SKU002", "SKU003", "SKU004", "SKU005" };
            var skuNames = new[] { "iPhone 15", "华为 Mate 60", "小米 14", "OPPO Find X7", "VIVO X100" };
            var batchNumbers = new[] { "BATCH001", "BATCH002", "BATCH003", "BATCH004", "BATCH005" };

            foreach (var order in _mockOrders.Take(10)) // 为前10个订单生成详情
            {
                int itemCount = _random.Next(1, 4); // 每个订单1-3个商品
                for (int i = 0; i < itemCount; i++)
                {
                    var skuIndex = _random.Next(skuCodes.Length);
                    var detail = new PkmOrderDetailModel
                    {
                        OrderNo = order.orderNo,
                        SkuCode = skuCodes[skuIndex],
                        SkuName = skuNames[skuIndex],
                        BatchNumber = batchNumbers[_random.Next(batchNumbers.Length)],
                        RequiredQuantity = _random.Next(1, 100),
                        PickedQuantity = 0,
                        Unit = "个"
                    };
                    details.Add(detail);
                }
            }

            return details;
        }
    }
}
