using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using WMS.MODEL;

namespace WMS.SERVICE
{
    public interface IOutboundService
    {
        Task<List<PkmOrderModel>> GetOutboundOrdersAsync(
            string systemOrderNo = null,
            string customerOrderNo = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int page = 1,
            int pageSize = 10);

        // 校验相关方法
        Task<bool> ValidateOrderNumberAsync(string orderNumber);
        Task<bool> ValidateSkuCodeAsync(string orderNumber, string skuCode);
        Task<bool> ValidateBatchNumberAsync(string orderNumber, string skuCode, string batchNumber);
        Task<decimal> GetRequiredQuantityAsync(string orderNumber, string skuCode, string batchNumber);
        Task<bool> SubmitPickingDataAsync(string orderNumber, string skuCode, string batchNumber, decimal quantity);
    }
}
